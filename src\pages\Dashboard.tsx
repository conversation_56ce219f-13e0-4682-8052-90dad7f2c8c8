import React, { useState } from "react";
import { ArrowUpRight, Users, Briefcase, Clock, Calendar, Download, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import StatCard from "@/components/dashboard/StatCard";
import Line<PERSON>hart from "@/components/dashboard/LineChart";
import Bar<PERSON>hart from "@/components/dashboard/BarChart";
import ExportReportModal from "@/components/dashboard/ExportReportModal";
import AddCandidateModal from "@/components/dashboard/AddCandidateModal";

// Import API hooks
import { useDashboardStats, useRecruitmentData, useSourceData } from "@/hooks/useAnalytics";
import { useUpcomingInterviews } from "@/hooks/useInterviews";
import { applicationApi } from "@/services/api";

/**
 * Dashboard page component
 * Displays key metrics and charts for the TalentSol ATS
 */
const Dashboard = () => {
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [addCandidateModalOpen, setAddCandidateModalOpen] = useState(false);

  // API hooks for real data
  const { stats: dashboardStats, loading: statsLoading } = useDashboardStats();
  const { data: recruitmentData, loading: recruitmentLoading } = useRecruitmentData();
  const { data: sourceData, loading: sourceLoading } = useSourceData();
  const { interviews: upcomingInterviews, loading: interviewsLoading } = useUpcomingInterviews();

  // State for recent applications
  const [recentApplications, setRecentApplications] = React.useState<any[]>([]);
  const [applicationsLoading, setApplicationsLoading] = React.useState(true);

  // State for top jobs
  const [topJobs, setTopJobs] = React.useState<any[]>([]);
  const [topJobsLoading, setTopJobsLoading] = React.useState(true);

  // Load recent applications and top jobs
  React.useEffect(() => {
    const loadData = async () => {
      try {
        setApplicationsLoading(true);
        setTopJobsLoading(true);

        const [applicationsResponse, topJobsResponse] = await Promise.all([
          applicationApi.getApplications({ limit: 5, sortBy: 'createdAt', sortOrder: 'desc' }),
          analyticsApi.getTopJobs(3)
        ]);

        setRecentApplications(applicationsResponse.data || []);
        setTopJobs(topJobsResponse.data?.jobs || []);
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
        setRecentApplications([]);
        setTopJobs([]);
      } finally {
        setApplicationsLoading(false);
        setTopJobsLoading(false);
      }
    };

    loadData();
  }, []);



  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-sm text-gray-500">
            Overview of your recruitment operations
          </p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setExportModalOpen(true)}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export Report
          </Button>
          <Button
            size="sm"
            className="bg-ats-blue hover:bg-ats-dark-blue flex items-center gap-2"
            onClick={() => setAddCandidateModalOpen(true)}
          >
            <Plus className="h-4 w-4" />
            Add Candidate
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Candidates"
          value={statsLoading ? "..." : (dashboardStats?.totalCandidates?.toString() || "0")}
          description="Last 30 days"
          icon={<Users className="h-4 w-4 text-ats-blue" />}
          change={dashboardStats?.changeMetrics?.totalCandidates ? {
            value: Math.abs(dashboardStats.changeMetrics.totalCandidates.change),
            positive: dashboardStats.changeMetrics.totalCandidates.change >= 0
          } : undefined}
        />
        <StatCard
          title="Open Positions"
          value={statsLoading ? "..." : (dashboardStats?.activeJobs?.toString() || "0")}
          description="Active job postings"
          icon={<Briefcase className="h-4 w-4 text-ats-blue" />}
          change={dashboardStats?.changeMetrics?.activeJobs ? {
            value: Math.abs(dashboardStats.changeMetrics.activeJobs.change),
            positive: dashboardStats.changeMetrics.activeJobs.change >= 0
          } : undefined}
        />
        <StatCard
          title="Time to Hire"
          value={statsLoading ? "..." : (dashboardStats?.timeToHire?.averageDays ? `${dashboardStats.timeToHire.averageDays} days` : "0 days")}
          description="Average this quarter"
          icon={<Clock className="h-4 w-4 text-ats-blue" />}
        />
        <StatCard
          title="Interviews This Week"
          value={interviewsLoading ? "..." : (upcomingInterviews?.length?.toString() || "0")}
          description={`${upcomingInterviews?.filter(i => {
            const today = new Date();
            const interviewDate = new Date(i.dateTime);
            return interviewDate.toDateString() === today.toDateString();
          }).length || 0} scheduled for today`}
          icon={<Calendar className="h-4 w-4 text-ats-blue" />}
          change={dashboardStats?.changeMetrics?.interviews ? {
            value: Math.abs(dashboardStats.changeMetrics.interviews.change),
            positive: dashboardStats.changeMetrics.interviews.change >= 0
          } : undefined}
        />
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {recruitmentLoading ? (
          <div className="bg-white p-6 rounded-lg border h-[300px] flex items-center justify-center">
            <div className="text-center text-gray-500">Loading recruitment data...</div>
          </div>
        ) : recruitmentData?.data && recruitmentData.data.length > 0 ? (
          <LineChart
            title="Recruitment Pipeline"
            description="Tracking applications, interviews, and offers"
            data={recruitmentData.data}
            lines={[
              { dataKey: "applications", stroke: "#3B82F6", name: "Applications" },
              { dataKey: "interviews", stroke: "#38BDF8", name: "Interviews" },
              { dataKey: "offers", stroke: "#4ADE80", name: "Offers" },
            ]}
          />
        ) : (
          <div className="bg-white p-6 rounded-lg border h-[300px] flex items-center justify-center">
            <div className="text-center text-gray-500">No recruitment data available</div>
          </div>
        )}

        {sourceLoading ? (
          <div className="bg-white p-6 rounded-lg border h-[300px] flex items-center justify-center">
            <div className="text-center text-gray-500">Loading source data...</div>
          </div>
        ) : sourceData?.sources && sourceData.sources.length > 0 ? (
          <BarChart
            title="Candidate Sources"
            description="Breakdown of candidates by source"
            data={sourceData.sources}
            bars={[{ dataKey: "candidates", fill: "#3B82F6", name: "Candidates" }]}
            vertical={true}
          />
        ) : (
          <div className="bg-white p-6 rounded-lg border h-[300px] flex items-center justify-center">
            <div className="text-center text-gray-500">No source data available</div>
          </div>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <div className="bg-white p-6 rounded-lg border h-[280px] flex flex-col justify-between">
          <div>
            <h3 className="text-lg font-medium">Upcoming Interviews</h3>
            <p className="text-sm text-gray-500">Next 7 days</p>
          </div>

          <div className="space-y-3 mt-4">
            {interviewsLoading ? (
              <div className="text-center text-gray-500 py-4">Loading interviews...</div>
            ) : upcomingInterviews && upcomingInterviews.length > 0 ? (
              upcomingInterviews.slice(0, 3).map((interview) => (
                <div key={interview.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <div>
                    <div className="font-medium">{interview.candidateName}</div>
                    <div className="text-xs text-gray-500">{interview.position} • {interview.type}</div>
                  </div>
                  <div className="text-xs font-medium">
                    {new Date(interview.dateTime).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      hour: 'numeric',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4">No upcoming interviews</div>
            )}
          </div>

          <Button variant="ghost" size="sm" className="justify-start w-fit px-0 text-ats-blue mt-3">
            View all interviews
            <ArrowUpRight className="ml-1 h-3 w-3" />
          </Button>
        </div>

        <div className="bg-white p-6 rounded-lg border h-[280px] flex flex-col justify-between">
          <div>
            <h3 className="text-lg font-medium">Recent Applications</h3>
            <p className="text-sm text-gray-500">Last 7 days</p>
          </div>

          <div className="space-y-3 mt-4">
            {applicationsLoading ? (
              <div className="text-center text-gray-500 py-4">Loading applications...</div>
            ) : recentApplications && recentApplications.length > 0 ? (
              recentApplications.slice(0, 3).map((application) => (
                <div key={application.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <div>
                    <div className="font-medium">{application.candidateName || `${application.firstName} ${application.lastName}`}</div>
                    <div className="text-xs text-gray-500">{application.jobTitle || application.position}</div>
                  </div>
                  <div className="text-xs font-medium">
                    {new Date(application.createdAt).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric'
                    })}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4">No recent applications</div>
            )}
          </div>

          <Button variant="ghost" size="sm" className="justify-start w-fit px-0 text-ats-blue mt-3">
            View all applications
            <ArrowUpRight className="ml-1 h-3 w-3" />
          </Button>
        </div>

        <div className="bg-white p-6 rounded-lg border h-[280px] flex flex-col justify-between">
          <div>
            <h3 className="text-lg font-medium">Top Job Openings</h3>
            <p className="text-sm text-gray-500">By application volume</p>
          </div>

          <div className="space-y-3 mt-4">
            {topJobsLoading ? (
              <div className="text-center text-gray-500 py-4">Loading top jobs...</div>
            ) : topJobs && topJobs.length > 0 ? (
              topJobs.map((job) => (
                <div key={job.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <div>
                    <div className="font-medium">{job.title}</div>
                    <div className="text-xs text-gray-500">{job.department} • {job.location}</div>
                  </div>
                  <div className="text-xs font-medium text-ats-blue">{job.applicationCount} applications</div>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4">No job openings available</div>
            )}
          </div>

          <Button variant="ghost" size="sm" className="justify-start w-fit px-0 text-ats-blue mt-3">
            View all jobs
            <ArrowUpRight className="ml-1 h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Modals */}
      <ExportReportModal
        open={exportModalOpen}
        onOpenChange={setExportModalOpen}
      />
      <AddCandidateModal
        open={addCandidateModalOpen}
        onOpenChange={setAddCandidateModalOpen}
      />
    </div>
  );
};

export default Dashboard;
