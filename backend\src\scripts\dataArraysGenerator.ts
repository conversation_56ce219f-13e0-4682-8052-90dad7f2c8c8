import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Data Arrays Generator for TalentSol ATS
 * Generates specific data arrays and structures for dashboard components
 */

interface TimeSeriesData {
  date: string;
  applications: number;
  interviews: number;
  hires: number;
  candidates: number;
}

interface SourceMetrics {
  source: string;
  candidates: number;
  applications: number;
  hires: number;
  conversionRate: number;
  averageScore: number;
}

interface DepartmentMetrics {
  department: string;
  openJobs: number;
  totalApplications: number;
  averageTimeToHire: number;
  hireRate: number;
}

interface CandidateJourney {
  candidateId: string;
  candidateName: string;
  applicationDates: Date[];
  interviewDates: Date[];
  statusProgression: Array<{
    status: string;
    date: Date;
    jobTitle: string;
  }>;
  totalDaysInPipeline: number;
  finalOutcome: string;
}

class DataArraysGenerator {
  
  async generateAllDataArrays(companyId: string) {
    console.log('📊 Generating comprehensive data arrays for dashboard...');

    try {
      const [
        timeSeriesData,
        sourceMetrics,
        departmentMetrics,
        candidateJourneys,
        pipelineData,
        performanceMetrics
      ] = await Promise.all([
        this.generateTimeSeriesData(companyId),
        this.generateSourceMetrics(companyId),
        this.generateDepartmentMetrics(companyId),
        this.generateCandidateJourneys(companyId),
        this.generatePipelineData(companyId),
        this.generatePerformanceMetrics(companyId)
      ]);

      const dataArrays = {
        timeSeries: timeSeriesData,
        sources: sourceMetrics,
        departments: departmentMetrics,
        candidateJourneys,
        pipeline: pipelineData,
        performance: performanceMetrics,
        generatedAt: new Date().toISOString(),
        candidateCentric: true,
      };

      console.log('✅ All data arrays generated successfully');
      return dataArrays;

    } catch (error) {
      console.error('❌ Data arrays generation failed:', error);
      throw error;
    }
  }

  private async generateTimeSeriesData(companyId: string): Promise<TimeSeriesData[]> {
    console.log('📈 Generating time series data...');

    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - 90 * 24 * 60 * 60 * 1000); // Last 90 days

    const timeSeriesData: TimeSeriesData[] = [];

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dayStart = new Date(d);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(d);
      dayEnd.setHours(23, 59, 59, 999);

      const [applications, interviews, hires, candidates] = await Promise.all([
        prisma.application.count({
          where: {
            job: { companyId },
            submittedAt: { gte: dayStart, lte: dayEnd }
          }
        }),
        prisma.interview.count({
          where: {
            application: { job: { companyId } },
            scheduledAt: { gte: dayStart, lte: dayEnd }
          }
        }),
        prisma.application.count({
          where: {
            job: { companyId },
            status: 'hired',
            hiredAt: { gte: dayStart, lte: dayEnd }
          }
        }),
        prisma.candidate.count({
          where: {
            createdAt: { gte: dayStart, lte: dayEnd },
            applications: { some: { job: { companyId } } }
          }
        })
      ]);

      timeSeriesData.push({
        date: d.toISOString().split('T')[0],
        applications,
        interviews,
        hires,
        candidates,
      });
    }

    return timeSeriesData;
  }

  private async generateSourceMetrics(companyId: string): Promise<SourceMetrics[]> {
    console.log('🎯 Generating source metrics...');

    const applications = await prisma.application.findMany({
      where: { job: { companyId } },
      include: { candidate: true },
      select: {
        candidateId: true,
        status: true,
        metadata: true,
        scoring: true,
        candidate: {
          select: { id: true }
        }
      }
    });

    const sourceMap = new Map<string, {
      candidateIds: Set<string>;
      applications: number;
      hires: number;
      totalScore: number;
      scoreCount: number;
    }>();

    applications.forEach(app => {
      const source = (app.metadata as any)?.source || 'unknown';
      
      if (!sourceMap.has(source)) {
        sourceMap.set(source, {
          candidateIds: new Set(),
          applications: 0,
          hires: 0,
          totalScore: 0,
          scoreCount: 0,
        });
      }

      const sourceData = sourceMap.get(source)!;
      sourceData.candidateIds.add(app.candidateId);
      sourceData.applications++;
      
      if (app.status === 'hired') {
        sourceData.hires++;
      }

      const score = (app.scoring as any)?.automaticScore;
      if (score && score > 0) {
        sourceData.totalScore += score;
        sourceData.scoreCount++;
      }
    });

    return Array.from(sourceMap.entries()).map(([source, data]) => ({
      source,
      candidates: data.candidateIds.size,
      applications: data.applications,
      hires: data.hires,
      conversionRate: data.applications > 0 ? (data.hires / data.applications) * 100 : 0,
      averageScore: data.scoreCount > 0 ? data.totalScore / data.scoreCount : 0,
    })).sort((a, b) => b.candidates - a.candidates);
  }

  private async generateDepartmentMetrics(companyId: string): Promise<DepartmentMetrics[]> {
    console.log('🏢 Generating department metrics...');

    const jobs = await prisma.job.findMany({
      where: { companyId },
      include: {
        applications: {
          include: {
            candidate: true
          }
        }
      }
    });

    const departmentMap = new Map<string, {
      openJobs: number;
      applications: any[];
      hires: any[];
    }>();

    jobs.forEach(job => {
      const dept = job.department || 'Unknown';
      
      if (!departmentMap.has(dept)) {
        departmentMap.set(dept, {
          openJobs: 0,
          applications: [],
          hires: [],
        });
      }

      const deptData = departmentMap.get(dept)!;
      
      if (job.status === 'open') {
        deptData.openJobs++;
      }

      deptData.applications.push(...job.applications);
      deptData.hires.push(...job.applications.filter(app => app.status === 'hired'));
    });

    return Array.from(departmentMap.entries()).map(([department, data]) => {
      const timeToHireData = data.hires
        .filter(hire => hire.hiredAt)
        .map(hire => Math.ceil((hire.hiredAt.getTime() - hire.submittedAt.getTime()) / (1000 * 60 * 60 * 24)));

      const averageTimeToHire = timeToHireData.length > 0
        ? timeToHireData.reduce((sum, days) => sum + days, 0) / timeToHireData.length
        : 0;

      return {
        department,
        openJobs: data.openJobs,
        totalApplications: data.applications.length,
        averageTimeToHire: Math.round(averageTimeToHire),
        hireRate: data.applications.length > 0 ? (data.hires.length / data.applications.length) * 100 : 0,
      };
    }).sort((a, b) => b.totalApplications - a.totalApplications);
  }

  private async generateCandidateJourneys(companyId: string): Promise<CandidateJourney[]> {
    console.log('🚶 Generating candidate journeys...');

    const candidates = await prisma.candidate.findMany({
      where: {
        applications: { some: { job: { companyId } } }
      },
      include: {
        applications: {
          where: { job: { companyId } },
          include: {
            job: { select: { title: true } },
            interviews: { select: { scheduledAt: true } }
          },
          orderBy: { submittedAt: 'asc' }
        }
      },
      take: 50 // Limit for performance
    });

    return candidates.map(candidate => {
      const applications = candidate.applications;
      const applicationDates = applications.map(app => app.submittedAt);
      const interviewDates = applications.flatMap(app => 
        app.interviews.map(interview => interview.scheduledAt)
      );

      const statusProgression = applications.map(app => ({
        status: app.status,
        date: app.submittedAt,
        jobTitle: app.job.title,
      }));

      const firstApplication = applications[0]?.submittedAt;
      const lastActivity = applications[applications.length - 1]?.submittedAt;
      const totalDaysInPipeline = firstApplication && lastActivity
        ? Math.ceil((lastActivity.getTime() - firstApplication.getTime()) / (1000 * 60 * 60 * 24))
        : 0;

      const finalOutcome = applications.find(app => ['hired', 'rejected'].includes(app.status))?.status || 'in_progress';

      return {
        candidateId: candidate.id,
        candidateName: `${candidate.firstName} ${candidate.lastName}`,
        applicationDates,
        interviewDates,
        statusProgression,
        totalDaysInPipeline,
        finalOutcome,
      };
    });
  }

  private async generatePipelineData(companyId: string) {
    console.log('🔄 Generating pipeline data...');

    const statusCounts = await prisma.application.groupBy({
      by: ['status'],
      where: { job: { companyId } },
      _count: { status: true }
    });

    const total = statusCounts.reduce((sum, item) => sum + item._count.status, 0);

    const pipelineStages = [
      { stage: 'Applied', status: 'applied', color: '#3B82F6' },
      { stage: 'Screening', status: 'screening', color: '#8B5CF6' },
      { stage: 'Interview', status: 'interview', color: '#06B6D4' },
      { stage: 'Assessment', status: 'assessment', color: '#10B981' },
      { stage: 'Offer', status: 'offer', color: '#F59E0B' },
      { stage: 'Hired', status: 'hired', color: '#EF4444' },
    ];

    return pipelineStages.map(stage => {
      const statusData = statusCounts.find(item => item.status === stage.status);
      const count = statusData?._count.status || 0;
      
      return {
        ...stage,
        count,
        percentage: total > 0 ? Math.round((count / total) * 100) : 0,
      };
    });
  }

  private async generatePerformanceMetrics(companyId: string) {
    console.log('📊 Generating performance metrics...');

    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

    const [currentPeriod, previousPeriod] = await Promise.all([
      this.getPeriodMetrics(companyId, thirtyDaysAgo, now),
      this.getPeriodMetrics(companyId, sixtyDaysAgo, thirtyDaysAgo)
    ]);

    const calculateChange = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return Math.round(((current - previous) / previous) * 100);
    };

    return {
      candidates: {
        current: currentPeriod.candidates,
        previous: previousPeriod.candidates,
        change: calculateChange(currentPeriod.candidates, previousPeriod.candidates),
      },
      applications: {
        current: currentPeriod.applications,
        previous: previousPeriod.applications,
        change: calculateChange(currentPeriod.applications, previousPeriod.applications),
      },
      interviews: {
        current: currentPeriod.interviews,
        previous: previousPeriod.interviews,
        change: calculateChange(currentPeriod.interviews, previousPeriod.interviews),
      },
      hires: {
        current: currentPeriod.hires,
        previous: previousPeriod.hires,
        change: calculateChange(currentPeriod.hires, previousPeriod.hires),
      },
      conversionRate: {
        current: currentPeriod.applications > 0 ? (currentPeriod.hires / currentPeriod.applications) * 100 : 0,
        previous: previousPeriod.applications > 0 ? (previousPeriod.hires / previousPeriod.applications) * 100 : 0,
      },
    };
  }

  private async getPeriodMetrics(companyId: string, startDate: Date, endDate: Date) {
    const [candidates, applications, interviews, hires] = await Promise.all([
      prisma.candidate.count({
        where: {
          createdAt: { gte: startDate, lte: endDate },
          applications: { some: { job: { companyId } } }
        }
      }),
      prisma.application.count({
        where: {
          job: { companyId },
          submittedAt: { gte: startDate, lte: endDate }
        }
      }),
      prisma.interview.count({
        where: {
          application: { job: { companyId } },
          scheduledAt: { gte: startDate, lte: endDate }
        }
      }),
      prisma.application.count({
        where: {
          job: { companyId },
          status: 'hired',
          hiredAt: { gte: startDate, lte: endDate }
        }
      })
    ]);

    return { candidates, applications, interviews, hires };
  }
}

// Export and execution
export { DataArraysGenerator };

// Script execution
async function runDataArraysGeneration() {
  const generator = new DataArraysGenerator();
  
  try {
    // Get the demo company
    const company = await prisma.company.findFirst({
      where: { name: 'TalentSol Demo Company' }
    });

    if (!company) {
      throw new Error('Demo company not found. Please run the synthetic data generator first.');
    }

    const dataArrays = await generator.generateAllDataArrays(company.id);
    
    console.log('\n📊 Data Arrays Summary:');
    console.log(`- Time Series Points: ${dataArrays.timeSeries.length}`);
    console.log(`- Source Metrics: ${dataArrays.sources.length}`);
    console.log(`- Department Metrics: ${dataArrays.departments.length}`);
    console.log(`- Candidate Journeys: ${dataArrays.candidateJourneys.length}`);
    console.log(`- Pipeline Stages: ${dataArrays.pipeline.length}`);
    
    console.log('\n🎉 Data arrays generation completed successfully!');
    return dataArrays;
    
  } catch (error) {
    console.error('❌ Data arrays generation failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runDataArraysGeneration();
}
