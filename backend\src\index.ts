import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import responseTime from 'response-time';
import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';

// Import cache system
import { initializeCache, cleanupCache, getCacheHealth } from './cache/index.js';

// Import routes
import authRoutes from './routes/auth.js';
import userRoutes from './routes/users.js';
import jobRoutes from './routes/jobs.js';
import candidateRoutes from './routes/candidates.js';
import applicationRoutes from './routes/applications.js';
import interviewRoutes from './routes/interviews.js';
import documentRoutes from './routes/documents.js';
import analyticsRoutes from './routes/analytics.js';
import mlRoutes from './routes/ml.js';
import notificationRoutes from './routes/notifications.js';

// Import middleware
import { errorHandler } from './middleware/errorHandler.js';
import { notFound } from './middleware/notFound.js';
import { authenticateToken } from './middleware/auth.js';

// Load environment variables
dotenv.config();

// Initialize Prisma client
export const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
});

// Create Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(responseTime());
app.use(limiter);
app.use(morgan(process.env.NODE_ENV === 'production' ? 'combined' : 'dev'));
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:8080',
  credentials: true,
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', async (req, res) => {
  const cacheHealth = await getCacheHealth();

  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    cache: cacheHealth,
  });
});

// Cache health endpoint
app.get('/health/cache', async (req, res) => {
  const cacheHealth = await getCacheHealth();
  res.status(200).json(cacheHealth);
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', authenticateToken, userRoutes);
app.use('/api/jobs', jobRoutes);
app.use('/api/candidates', authenticateToken, candidateRoutes);
app.use('/api/applications', applicationRoutes);
app.use('/api/interviews', authenticateToken, interviewRoutes);
app.use('/api/documents', authenticateToken, documentRoutes);
app.use('/api/analytics', authenticateToken, analyticsRoutes);
app.use('/api/ml', authenticateToken, mlRoutes);
app.use('/api/notifications', authenticateToken, notificationRoutes);

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Initialize cache system
async function startServer() {
  try {
    // Initialize cache system
    await initializeCache();

    // Start server
    app.listen(PORT, () => {
      console.log(`🚀 TalentSol ATS Backend running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV}`);
      console.log(`🔗 CORS enabled for: ${process.env.CORS_ORIGIN}`);
      console.log(`💾 Cache system initialized`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Received SIGINT, shutting down gracefully...');
  await cleanupCache();
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  await cleanupCache();
  await prisma.$disconnect();
  process.exit(0);
});

// Start the server
startServer();

export default app;
