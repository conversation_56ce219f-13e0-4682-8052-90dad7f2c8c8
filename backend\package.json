{"name": "talentsol-ats-backend", "version": "1.0.0", "description": "Backend API for TalentSol ATS", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "tsx src/seed.ts", "db:enhance": "tsx src/scripts/enhanceSyntheticData.ts", "db:seed-enhanced": "npm run db:seed && npm run db:enhance", "db:check": "tsx src/scripts/checkDatabase.ts", "setup-unified-data": "tsx src/scripts/setupUnifiedData.ts", "generate-synthetic": "tsx src/scripts/syntheticDataGenerator.ts", "generate-batch": "tsx src/scripts/batchDataGeneration.ts", "generate-minimal": "tsx src/scripts/generateMinimalData.ts", "validate-data": "tsx src/scripts/validateSyntheticData.ts", "data-minimal": "npm run db:check && npm run generate-minimal && npm run validate-data", "data-full": "npm run db:check && npm run generate-batch && npm run validate-data", "db:studio": "prisma studio", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.23.8", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "redis": "^4.6.12", "ioredis": "^5.3.2", "js-yaml": "^4.1.0", "node-cache": "^5.1.2", "response-time": "^2.3.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/compression": "^1.7.5", "@types/node": "^20.10.5", "@types/js-yaml": "^4.0.9", "@types/response-time": "^2.3.8", "prisma": "^5.7.1", "tsx": "^4.6.2", "typescript": "^5.3.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}, "keywords": ["ats", "recruitment", "api", "express", "prisma", "typescript"], "author": "<PERSON> (<PERSON><PERSON><PERSON>) <PERSON>", "license": "MIT"}